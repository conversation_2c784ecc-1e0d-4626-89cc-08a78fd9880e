'use client'

import React from 'react'
import { Brain, Cpu, GraduationCap } from 'lucide-react'
import { motion } from "framer-motion"

function FeaturesSection() {
  const features = [
    {
      name: 'AI智能标注',
      description: '提供高精度的数据标注服务，支持图像、文本、语音等多模态数据，为机器学习模型训练提供优质数据集。',
      icon: Brain,
      color: 'from-blue-500 to-blue-600',
      bgGradient: 'from-blue-50 to-indigo-50',
      details: ['图像标注', '文本标注', '语音标注', '视频标注', '3D点云标注'],
      stats: { accuracy: '99.5%', speed: '10x', projects: '1000+' },
      benefits: ['提升标注精度', '降低人工成本', '加速模型训练', '保证数据质量']
    },
    {
      name: 'CPU算力租用',
      description: '灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求场景。',
      icon: C<PERSON>,
      color: 'from-green-500 to-green-600',
      bgGradient: 'from-green-50 to-emerald-50',
      details: ['高性能计算', '弹性扩容', '按需付费', '24/7监控', '数据安全'],
      stats: { performance: '100x', availability: '99.9%', savings: '60%' },
      benefits: ['按需弹性扩容', '降低硬件成本', '专业运维保障', '全球就近部署']
    },
    {
      name: '教育培训管理',
      description: '一站式教育培训管理平台，涵盖课程管理、学员管理、考试系统、证书颁发等完整教育生态链。',
      icon: GraduationCap,
      color: 'from-purple-500 to-purple-600',
      bgGradient: 'from-purple-50 to-violet-50',
      details: ['课程管理', '在线考试', '学员跟踪', '证书系统', '数据分析'],
      stats: { students: '10万+', courses: '1000+', satisfaction: '98%' },
      benefits: ['统一管理平台', '智能学习分析', '自动化考试', '数字化证书']
    }
  ]

  return (
    <section className="py-32 sm:py-40 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50/40 via-indigo-50/20 to-slate-50" />
      <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(59, 130, 246, 0.06)' }} />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(37, 99, 235, 0.04)' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          className="mx-auto max-w-3xl text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4" style={{ color: 'rgb(59 130 246)' }}>技术创新</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            引领行业数字化转型
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            秉持"技术引领未来，创新驱动发展"的理念，我们致力于将前沿科技转化为实际应用，为客户创造价值。
          </p>
        </motion.div>

        <div className="mx-auto mt-20 max-w-2xl sm:mt-24 lg:mt-32 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-20 lg:max-w-none lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <FeatureCard feature={feature} />
              </motion.div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection


// Feature Card Component
function FeatureCard({ feature }: { feature: any }) {
  return (
    <div className="relative group h-full">
      <div className={`card-modern p-8 h-full hover-glow bg-gradient-to-br ${feature.bgGradient} border-0 shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105`}>
        {/* 图标和标题区域 */}
        <div className="text-center mb-8">
          <div className="relative inline-block mb-6">
            <div className={`flex h-20 w-20 items-center justify-center rounded-3xl shadow-lg bg-gradient-to-br ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
              <feature.icon className="h-10 w-10 text-white" aria-hidden="true" />
            </div>
            <div className="absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />

            {/* 装饰性光环 */}
            <div className="absolute -inset-4 rounded-full border-2 border-blue-200/30 opacity-0 group-hover:opacity-100 animate-pulse transition-opacity duration-300" />
          </div>

          <h3 className="text-2xl font-bold text-slate-800 mb-3 group-hover:text-gradient-modern transition-all duration-300">
            {feature.name}
          </h3>
        </div>

        {/* 描述 */}
        <p className="text-slate-600 leading-relaxed mb-8 text-center">
          {feature.description}
        </p>

        {/* 核心优势 */}
        <div className="mb-8">
          <h4 className="text-sm font-semibold text-slate-700 mb-4 text-center">核心优势</h4>
          <div className="grid grid-cols-2 gap-3">
            {feature.benefits.map((benefit: string, index: number) => (
              <motion.div
                key={benefit}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-2 text-sm text-slate-600"
              >
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex-shrink-0" />
                <span>{benefit}</span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* 技术特性标签 */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {feature.details.map((detail: string, index: number) => (
            <span
              key={detail}
              className="inline-flex items-center rounded-full px-3 py-1.5 text-xs font-medium transition-all duration-200 hover-lift"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.08)',
                color: 'rgb(59 130 246)',
                animationDelay: `${index * 100}ms`
              }}
            >
              {detail}
            </span>
          ))}
        </div>

        {/* 统计数据 */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t border-slate-200/50">
          {Object.entries(feature.stats).map(([key, value], index) => (
            <motion.div
              key={key}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-lg font-bold text-blue-600 mb-1">{String(value)}</div>
              <div className="text-xs text-slate-500 capitalize">{key}</div>
            </motion.div>
          ))}
        </div>

        {/* 悬停效果底边 */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r ${feature.color}`} />

        {/* 背景装饰 */}
        <div className="absolute top-4 right-4 w-24 h-24 rounded-full opacity-5 group-hover:opacity-10 transition-opacity duration-300" style={{ background: `linear-gradient(135deg, ${feature.color.replace('from-', '').replace(' to-', ', ')})` }} />
      </div>
    </div>
  )
}