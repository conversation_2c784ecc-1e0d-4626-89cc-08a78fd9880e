
import StatsSection from "@/components/home/<USER>"
import HeroSection from "@/components/home/<USER>"
import FeaturesSection from "@/components/home/<USER>"
import SolutionsSection from "@/components/home/<USER>"
import TestimonialsSection from "@/components/home/<USER>"
import PartnersSection from "@/components/home/<USER>"
import CTASection from "@/components/home/<USER>"
import ServicesOverview from "@/components/home/<USER>"
import TechnologyStack from "@/components/home/<USER>"
import ProcessFlow from "@/components/home/<USER>"
import ClientShowcase from "@/components/home/<USER>"

export default function Home() {
  return (
    <div className="relative overflow-hidden">
      {/* Hero Section - 主视觉区域 */}
      <HeroSection />

      {/* Services Overview - 业务概览 */}
      <ServicesOverview />

      {/* Stats Section - 数据统计 */}
      <StatsSection />

      {/* Features Section - 核心功能 */}
      <FeaturesSection />

      {/* Process Flow - 业务流程 */}
      <ProcessFlow />

      {/* Technology Stack - 技术栈 */}
      <TechnologyStack />

      {/* Solutions Section - 解决方案 */}
      <SolutionsSection />

      {/* Client Showcase - 客户案例 */}
      <ClientShowcase />

      {/* Testimonials Section - 客户评价 */}
      <TestimonialsSection />

      {/* Partners Section - 合作伙伴 */}
      <PartnersSection />

      {/* CTA Section - 行动召唤 */}
      <CTASection />
    </div>
  )
}


