import { notFound } from "next/navigation"
import { ProductDetail } from "./components/ProductDetail"
import { Metadata } from 'next'
import { productDetails } from "@/lib/products-data"

type Benefit = {
  title: string;
  description: string;
}

type Product = {
  name: string;
  description: string;
  features: string[];
  techSpecs: {
    deployment: string;
    security: string;
    availability: string;
    support: string;
  };
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
}

async function getProduct(slug: string): Promise<Product | null> {
  const product = productDetails[slug as keyof typeof productDetails]
  return product || null
}

async function getAllProductSlugs() {
  return Object.keys(productDetails)
}

export async function generateStaticParams() {
  const slugs = await getAllProductSlugs()
  return slugs.map((slug:any) => ({
    slug: slug,
  }))
}


export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  const product = await getProduct(params.slug)
  
  if (!product) {
    return {
      title: '产品未找到',
    }
  }

  return {
    title: `${product.name} - 零点科技`,
    description: product.description,
  }
}

export default async function ProductPage({ params }: { params: { slug: string } }) {
  const product = await getProduct(params.slug)

  if (!product) {
    notFound()
  }

  return <ProductDetail product={product} />
}