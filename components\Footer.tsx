"use client"

import { <PERSON>ith<PERSON>, X, Linkedin, Mail } from 'lucide-react'
import Link from 'next/link'
import { Button } from './ui/button'

const Footer = () => {
  return (
    <footer className="border-t">
      <div className="mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8">
        <div className="flex justify-center space-x-6 md:order-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="https://x.com" target="_blank">
              <X className="h-5 w-5" />
              <span className="sr-only">X</span>
            </Link>
          </Button>
          <Button variant="ghost" size="icon" asChild>
            <Link href="https://github.com" target="_blank">
              <Github className="h-5 w-5" />
              <span className="sr-only">GitHub</span>
            </Link>
          </Button>
          <Button variant="ghost" size="icon" asChild>
            <Link href="https://linkedin.com" target="_blank">
              <Linkedin className="h-5 w-5" />
              <span className="sr-only">LinkedIn</span>
            </Link>
          </Button>
          <Button variant="ghost" size="icon" asChild>
            <Link href="mailto:<EMAIL>">
              <Mail className="h-5 w-5" />
              <span className="sr-only">Email</span>
            </Link>
          </Button>
        </div>
        <div className="mt-8 md:order-1 md:mt-0">
          <nav className="flex justify-center space-x-6"> 
            <Link href="/privacy" className="text-sm leading-6 text-muted-foreground hover:text-primary">
              Privacy
            </Link>
            <Link href="/cookies" className="text-sm leading-6 text-muted-foreground hover:text-primary">
              Cookies
            </Link>
            <Link href="/help" className="text-sm leading-6 text-muted-foreground hover:text-primary">
              Help
            </Link>
          </nav>
          <p className="mt-4 text-center text-xs leading-5 text-muted-foreground">
            &copy; {new Date().getFullYear()} 0dot. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer