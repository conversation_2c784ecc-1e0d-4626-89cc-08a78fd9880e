"use client"

import { motion } from "framer-motion"
import { Shield, Zap, Users, Globe, Clock, Award } from "lucide-react"

const features = [
  {
    icon: Shield,
    title: "安全可靠",
    description: "企业级安全保障，数据加密存储，符合国际安全标准",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Zap,
    title: "高性能",
    description: "优化的算法架构，毫秒级响应，支持大规模并发处理",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Users,
    title: "专业团队",
    description: "资深技术专家团队，7x24小时技术支持和服务保障",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Globe,
    title: "全球部署",
    description: "多地域云端部署，CDN加速，确保全球用户访问体验",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Clock,
    title: "快速交付",
    description: "标准化部署流程，快速上线，最短1天即可投入使用",
    color: "from-indigo-500 to-blue-500"
  },
  {
    icon: Award,
    title: "行业认证",
    description: "通过ISO27001、SOC2等多项国际认证，品质有保障",
    color: "from-yellow-500 to-orange-500"
  }
]

export function ProductFeatures() {
  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-slate-50 to-white" />
      <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl opacity-20" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 rounded-full blur-3xl opacity-20" style={{ background: 'rgba(37, 99, 235, 0.2)' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-3xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4" style={{ color: 'rgb(59 130 246)' }}>
            核心优势
          </h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl text-gradient-modern mb-6">
            为什么选择我们
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            专业的技术实力、完善的服务体系、可靠的安全保障，为您的业务发展提供强有力的支撑
          </p>
        </motion.div>

        {/* 特性网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="group relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="card-modern p-8 h-full hover-glow text-center" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
                {/* 图标 */}
                <div className="relative mx-auto w-16 h-16 mb-6">
                  <div 
                    className="absolute inset-0 rounded-2xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"
                    style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }}
                  />
                  <div className="relative flex items-center justify-center w-full h-full rounded-2xl bg-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="w-8 h-8" style={{ color: 'rgb(59 130 246)' }} />
                  </div>
                </div>

                {/* 内容 */}
                <h3 className="text-xl font-semibold text-slate-800 mb-4 group-hover:text-gradient-modern transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>

                {/* 装饰线条 */}
                <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235))' }} />
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部CTA */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-white/80 backdrop-blur-sm border shadow-lg" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: 'rgb(59 130 246)' }}></div>
            <span className="text-sm font-medium text-slate-700">
              已为 <span className="font-bold" style={{ color: 'rgb(59 130 246)' }}>2000+</span> 家企业提供专业服务
            </span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
