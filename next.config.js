/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  
  // Build optimizations
  swcMinify: true,
  poweredByHeader: false,
  
  // Compiler optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Static asset optimizations
  images: { 
    unoptimized: true, // Required for static export
    domains: [], // Add allowed image domains if needed
  },

  // Build-time optimizations
  eslint: {
    ignoreDuringBuilds: true,
    dirs: ['pages', 'components', 'lib', 'utils', 'hooks'],
  },

  // Experimental features
  experimental: {
    // Remove features not compatible with static export
  },

  // Runtime optimizations
  reactStrictMode: true,
  
  // Webpack configuration optimizations
  webpack: (config, { dev, isServer }) => {
    // Production-specific optimizations
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        minSize: 20000,
        maxSize: 244000,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        cacheGroups: {
          defaultVendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
        },
      };
    }

    // Optimize SVG imports
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },

  // Environment variable configuration
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  },
};

module.exports = nextConfig;