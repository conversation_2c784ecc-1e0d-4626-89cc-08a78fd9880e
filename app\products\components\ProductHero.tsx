"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON>rkles, Zap, Target } from "lucide-react"
import { Input } from "@/components/ui/input"

interface ProductHeroProps {
  searchQuery: string
  onSearchChange: (query: string) => void
}

export function ProductHero({ searchQuery, onSearchChange }: ProductHeroProps) {
  return (
    <section className="relative py-24 sm:py-32 overflow-hidden">
      {/* 现代化背景层 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/20" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.08)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(37,99,235,0.06)_0%,transparent_50%)]" />
        <div className="absolute inset-0 grid-bg opacity-20" />
      </div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-64 h-64 rounded-full blur-3xl animate-float opacity-40" style={{ background: 'rgba(59, 130, 246, 0.06)' }} />
      <div className="absolute bottom-20 right-10 w-80 h-80 rounded-full blur-3xl animate-float opacity-30" style={{ background: 'rgba(37, 99, 235, 0.04)', animationDelay: '3s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          {/* 标题区域 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full backdrop-blur-sm border mb-6" style={{ backgroundColor: 'rgba(59, 130, 246, 0.08)', borderColor: 'rgba(59, 130, 246, 0.2)' }}>
              <Sparkles className="w-4 h-4" style={{ color: 'rgb(59 130 246)' }} />
              <span className="text-sm font-semibold" style={{ color: 'rgb(59 130 246)' }}>创新产品服务</span>
            </div>
            
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl text-gradient-modern mb-6">
              智能化解决方案
            </h1>
            
            <p className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto mb-12">
              从AI智能标注到云计算服务，从教育管理到数据处理，我们为您提供全方位的技术解决方案，助力企业数字化转型
            </p>
          </motion.div>

          {/* 搜索区域 */}
          <motion.div
            className="max-w-2xl mx-auto mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative">
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
              <Input
                type="text"
                placeholder="搜索产品和服务..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-12 pr-4 py-4 text-lg rounded-2xl border-0 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 focus:ring-2 focus:ring-offset-2"
                style={{ 
                  ringColor: 'rgba(59, 130, 246, 0.3)',
                  boxShadow: '0 10px 30px rgba(59, 130, 246, 0.1)'
                }}
              />
            </div>
          </motion.div>

          {/* 特色亮点 */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            {[
              {
                icon: Target,
                title: "精准定制",
                description: "根据您的业务需求量身定制解决方案"
              },
              {
                icon: Zap,
                title: "快速部署",
                description: "专业团队支持，快速上线投入使用"
              },
              {
                icon: Sparkles,
                title: "持续创新",
                description: "不断迭代优化，保持技术领先优势"
              }
            ].map((item, index) => (
              <div key={index} className="group">
                <div className="flex flex-col items-center text-center p-6 rounded-2xl bg-white/60 backdrop-blur-sm border hover:bg-white/80 transition-all duration-300 hover-lift" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
                  <div className="p-3 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05))' }}>
                    <item.icon className="w-6 h-6" style={{ color: 'rgb(59 130 246)' }} />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-800 mb-2">{item.title}</h3>
                  <p className="text-sm text-slate-600">{item.description}</p>
                </div>
              </div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
