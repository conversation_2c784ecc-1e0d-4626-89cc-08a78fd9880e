"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Brain, Eye, Database, Target, CheckCircle2, 
  ArrowRight, Users, Clock, Shield, Zap 
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const features = [
  {
    icon: Eye,
    title: "图像标注",
    description: "支持目标检测、图像分割、关键点标注等多种图像标注任务",
    details: ["边界框标注", "多边形标注", "语义分割", "实例分割", "关键点标注"]
  },
  {
    icon: Database,
    title: "文本标注",
    description: "提供实体识别、情感分析、文本分类等NLP标注服务",
    details: ["命名实体识别", "情感分析", "文本分类", "关系抽取", "意图识别"]
  },
  {
    icon: Target,
    title: "语音标注",
    description: "专业的语音识别、说话人识别、情感识别标注服务",
    details: ["语音转文本", "说话人分离", "情感识别", "语音分类", "音频分割"]
  },
  {
    icon: CheckCircle2,
    title: "质量保证",
    description: "多层质检机制，确保标注数据的准确性和一致性",
    details: ["三级质检", "交叉验证", "一致性检查", "专家审核", "质量报告"]
  }
]

const advantages = [
  {
    icon: Users,
    title: "专业团队",
    description: "拥有500+专业标注员，覆盖多个领域专业知识"
  },
  {
    icon: Clock,
    title: "快速交付",
    description: "平均交付周期缩短50%，支持紧急项目加急处理"
  },
  {
    icon: Shield,
    title: "数据安全",
    description: "ISO27001认证，多重加密保护，确保数据安全"
  },
  {
    icon: Zap,
    title: "高效工具",
    description: "自研标注工具，支持批量操作，提升标注效率"
  }
]

const useCases = [
  {
    title: "自动驾驶",
    description: "为自动驾驶算法提供道路场景、交通标志、行人车辆等标注数据",
    image: "https://picsum.photos/seed/autonomous/400/300"
  },
  {
    title: "医疗影像",
    description: "医学影像标注，包括CT、MRI、X光片等医疗图像的病灶标注",
    image: "https://picsum.photos/seed/medical/400/300"
  },
  {
    title: "智能客服",
    description: "对话数据标注，意图识别，情感分析，提升客服机器人准确率",
    image: "https://picsum.photos/seed/chatbot/400/300"
  }
]

export default function AIAnnotationPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-8"
            >
              <Brain className="h-16 w-16 mx-auto text-primary" />
            </motion.div>
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl"
            >
              AI智能标注平台
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="mt-6 text-lg leading-8 text-muted-foreground"
            >
              专业的AI数据标注服务，为您的机器学习项目提供高质量训练数据
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="mt-10 flex items-center justify-center gap-x-6"
            >
              <Button size="lg" asChild>
                <Link href="/contact-us">
                  立即咨询
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                查看案例
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              核心功能
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              覆盖图像、文本、语音等多模态数据标注需求
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-2">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full">
                  <CardHeader>
                    <div className="flex items-center gap-4">
                      <div className="rounded-lg bg-primary/10 p-2">
                        <feature.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle>{feature.title}</CardTitle>
                        <CardDescription>{feature.description}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {feature.details.map((detail) => (
                        <Badge key={detail} variant="secondary">
                          {detail}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              我们的优势
            </h2>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-4">
            {advantages.map((advantage, index) => (
              <motion.div
                key={advantage.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="mx-auto mb-4 rounded-lg bg-primary/10 p-3 w-fit">
                  <advantage.icon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">{advantage.title}</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {advantage.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              应用场景
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              广泛应用于各个行业的AI项目
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
            {useCases.map((useCase, index) => (
              <motion.div
                key={useCase.title}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative overflow-hidden rounded-2xl group"
              >
                <div className="aspect-[4/3] relative">
                  <Image
                    src={useCase.image}
                    alt={useCase.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-black/0" />
                </div>
                <div className="absolute bottom-0 p-6 text-white">
                  <h3 className="text-xl font-semibold mb-2">{useCase.title}</h3>
                  <p className="text-sm text-white/80">{useCase.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32 bg-primary/5">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              准备开始您的AI项目了吗？
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              联系我们获取专业的数据标注服务方案
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact-us">
                  获取报价
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                下载产品手册
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
