"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"

interface Category {
  id: string
  name: string
  count: number
}

interface ProductCategoriesProps {
  categories: Category[]
  selectedCategory: string
  onCategoryChange: (category: string) => void
}

export function ProductCategories({ categories, selectedCategory, onCategoryChange }: ProductCategoriesProps) {
  return (
    <section className="py-16 border-b border-slate-100">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-2xl font-bold text-slate-800 mb-4">产品分类</h2>
          <p className="text-slate-600">选择您感兴趣的产品类别</p>
        </motion.div>

        <motion.div
          className="flex flex-wrap justify-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              onClick={() => onCategoryChange(category.id)}
              className={`
                relative px-6 py-3 rounded-xl font-medium transition-all duration-300 hover-lift
                ${selectedCategory === category.id
                  ? 'text-white shadow-lg'
                  : 'bg-white/80 backdrop-blur-sm border text-slate-700 hover:bg-white hover:shadow-md'
                }
              `}
              style={{
                background: selectedCategory === category.id 
                  ? 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))'
                  : undefined,
                borderColor: selectedCategory === category.id 
                  ? 'transparent' 
                  : 'rgba(59, 130, 246, 0.1)'
              }}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="relative z-10 flex items-center gap-2">
                {category.name}
                <Badge 
                  variant="secondary" 
                  className={`
                    text-xs
                    ${selectedCategory === category.id
                      ? 'bg-white/20 text-white border-white/30'
                      : 'bg-slate-100 text-slate-600'
                    }
                  `}
                >
                  {category.count}
                </Badge>
              </span>
              
              {/* 选中状态的背景动画 */}
              {selectedCategory === category.id && (
                <motion.div
                  className="absolute inset-0 rounded-xl"
                  style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05))' }}
                  layoutId="categoryBackground"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* 分类描述 */}
        <motion.div
          className="mt-8 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          {selectedCategory !== 'all' && (
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/60 backdrop-blur-sm border" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
              <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'rgb(59 130 246)' }}></div>
              <span className="text-sm text-slate-600">
                当前显示 <span className="font-semibold" style={{ color: 'rgb(59 130 246)' }}>
                  {categories.find(c => c.id === selectedCategory)?.name}
                </span> 分类下的 {categories.find(c => c.id === selectedCategory)?.count} 个产品
              </span>
            </div>
          )}
        </motion.div>
      </div>
    </section>
  )
}
