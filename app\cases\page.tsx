"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Brain, Cpu, GraduationCap, ArrowRight, 
  TrendingUp, Users, Clock, Target 
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const cases = [
  {
    id: 1,
    title: "智能驾驶数据标注项目",
    client: "某知名汽车制造商",
    category: "AI智能标注",
    icon: Brain,
    challenge: "需要对大量道路场景图像进行精确标注，包括车辆、行人、交通标志等多类别目标检测",
    solution: "采用我们的AI智能标注平台，组建专业标注团队，建立三级质检体系",
    results: [
      "标注数据量：500万张图像",
      "标注精度：99.5%",
      "项目周期：缩短40%",
      "成本节省：60%"
    ],
    image: "https://picsum.photos/seed/autonomous-driving/600/400",
    tags: ["自动驾驶", "图像标注", "目标检测"]
  },
  {
    id: 2,
    title: "科研院所高性能计算项目",
    client: "中科院某研究所",
    category: "CPU算力租用",
    icon: Cpu,
    challenge: "大规模科学计算任务需要强大的计算资源，自建成本高昂且资源利用率不高",
    solution: "提供弹性CPU集群租用服务，支持任务调度和资源动态分配",
    results: [
      "计算资源：10,000核心",
      "任务完成时间：提升3倍",
      "成本降低：70%",
      "资源利用率：95%"
    ],
    image: "https://picsum.photos/seed/hpc-computing/600/400",
    tags: ["科学计算", "高性能计算", "弹性扩容"]
  },
  {
    id: 3,
    title: "在线教育平台建设",
    client: "某大型教育集团",
    category: "教育培训管理",
    icon: GraduationCap,
    challenge: "传统线下教学模式无法满足疫情期间的教学需求，急需数字化转型",
    solution: "部署完整的教育培训管理系统，包括课程管理、在线考试、学员跟踪等功能",
    results: [
      "服务学员：50,000+",
      "课程数量：1,000+",
      "考试场次：10,000+",
      "满意度：98%"
    ],
    image: "https://picsum.photos/seed/online-education/600/400",
    tags: ["在线教育", "数字化转型", "教学管理"]
  },
  {
    id: 4,
    title: "金融风控模型训练",
    client: "某股份制银行",
    category: "AI智能标注",
    icon: Brain,
    challenge: "需要对海量金融交易数据进行标注，用于训练反欺诈和风险评估模型",
    solution: "提供专业的文本和结构化数据标注服务，确保数据质量和安全性",
    results: [
      "标注数据：1000万条记录",
      "模型准确率：提升25%",
      "风险识别率：99.2%",
      "处理时间：缩短80%"
    ],
    image: "https://picsum.photos/seed/fintech/600/400",
    tags: ["金融科技", "风控模型", "数据标注"]
  },
  {
    id: 5,
    title: "游戏公司渲染农场",
    client: "某知名游戏公司",
    category: "CPU算力租用",
    icon: Cpu,
    challenge: "游戏开发过程中需要大量渲染计算，峰值需求波动大，自建成本高",
    solution: "提供按需扩容的CPU集群服务，支持渲染任务的分布式处理",
    results: [
      "渲染任务：日均1000+",
      "渲染速度：提升5倍",
      "成本优化：50%",
      "资源弹性：秒级扩容"
    ],
    image: "https://picsum.photos/seed/game-rendering/600/400",
    tags: ["游戏开发", "渲染计算", "分布式处理"]
  },
  {
    id: 6,
    title: "职业技能培训平台",
    client: "某职业教育机构",
    category: "教育培训管理",
    icon: GraduationCap,
    challenge: "多地分校管理困难，学员学习进度难以跟踪，证书管理混乱",
    solution: "建设统一的教育培训管理平台，实现多校区统一管理和数字化证书",
    results: [
      "管理分校：20个",
      "培训学员：30,000+",
      "证书颁发：15,000+",
      "管理效率：提升60%"
    ],
    image: "https://picsum.photos/seed/vocational-training/600/400",
    tags: ["职业教育", "多校区管理", "数字证书"]
  }
]

const stats = [
  { label: "成功案例", value: "200+", icon: Target },
  { label: "服务客户", value: "500+", icon: Users },
  { label: "项目完成率", value: "99.8%", icon: TrendingUp },
  { label: "客户满意度", value: "98%", icon: Clock }
]

export default function CasesPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl"
            >
              成功案例
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-muted-foreground"
            >
              真实案例见证我们的专业实力，与众多知名企业共同成长
            </motion.p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="mx-auto mb-4 rounded-lg bg-primary/10 p-3 w-fit">
                  <stat.icon className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl font-bold text-primary">{stat.value}</div>
                <div className="mt-2 text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Cases Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid gap-8 lg:grid-cols-2">
            {cases.map((caseItem, index) => (
              <motion.div
                key={caseItem.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full overflow-hidden group hover:shadow-lg transition-shadow duration-300">
                  <div className="aspect-[3/2] relative overflow-hidden">
                    <Image
                      src={caseItem.image}
                      alt={caseItem.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
                        {caseItem.category}
                      </Badge>
                    </div>
                  </div>
                  <CardHeader>
                    <div className="flex items-start gap-4">
                      <div className="rounded-lg bg-primary/10 p-2">
                        <caseItem.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-xl">{caseItem.title}</CardTitle>
                        <CardDescription className="mt-1">{caseItem.client}</CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-sm text-muted-foreground mb-2">挑战</h4>
                      <p className="text-sm">{caseItem.challenge}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-muted-foreground mb-2">解决方案</h4>
                      <p className="text-sm">{caseItem.solution}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-sm text-muted-foreground mb-2">成果</h4>
                      <ul className="text-sm space-y-1">
                        {caseItem.results.map((result, idx) => (
                          <li key={idx} className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex flex-wrap gap-2 pt-2">
                      {caseItem.tags.map((tag) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 sm:py-32 bg-primary/5">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              成为我们的下一个成功案例
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              让我们为您提供专业的解决方案，助力您的业务成功
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg" asChild>
                <Link href="/contact-us">
                  开始合作
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                了解更多
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
