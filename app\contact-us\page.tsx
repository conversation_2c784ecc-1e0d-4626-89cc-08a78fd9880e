"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  Phone,
  Mail,
  MapPin,
  MessageSquare,
  Building2,
  Headphones,
  Users,
  Clock,
  Globe,
  Shield,
  Zap,
  Star,
  ArrowRight,
  CheckCircle,
  HelpCircle,
  MessageCircle
} from "lucide-react"
import ContactUsForm from './components/contactUsFrom'

const contactMethods = [
  {
    icon: Phone,
    title: "客服热线",
    value: "************",
    desc: "周一至周五 9:00-18:00",
    available: true,
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: Mail,
    title: "电子邮箱",
    value: "<EMAIL>",
    desc: "我们将在24小时内回复",
    available: true,
    color: "from-green-500 to-green-600"
  },
  {
    icon: MapPin,
    title: "公司地址",
    value: "北京市朝阳区科技园区88号零点大厦",
    desc: "欢迎来访交流",
    available: true,
    color: "from-purple-500 to-purple-600"
  }
]

const departments = [
  {
    icon: Headphones,
    title: "技术支持",
    email: "<EMAIL>",
    desc: "产品使用问题咨询",
    responseTime: "2小时内",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Building2,
    title: "商务合作",
    email: "<EMAIL>",
    desc: "商业合作洽谈",
    responseTime: "24小时内",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: MessageSquare,
    title: "媒体咨询",
    email: "<EMAIL>",
    desc: "媒体采访及品牌合作",
    responseTime: "12小时内",
    color: "from-purple-500 to-violet-500"
  },
  {
    icon: Users,
    title: "人才招聘",
    email: "<EMAIL>",
    desc: "加入我们，共创未来",
    responseTime: "48小时内",
    color: "from-orange-500 to-red-500"
  }
]

const faqs = [
  {
    question: "如何开始使用零点科技的服务？",
    answer: "您可以通过我们的官网注册账户，或直接联系我们的销售团队获取详细的产品演示和定制方案。"
  },
  {
    question: "技术支持的响应时间是多久？",
    answer: "我们承诺在工作时间内2小时内响应技术支持请求，紧急问题可通过热线电话获得即时支持。"
  },
  {
    question: "是否提供免费试用？",
    answer: "是的，我们为所有新客户提供14天的免费试用期，让您充分体验我们的产品和服务。"
  },
  {
    question: "如何保证数据安全？",
    answer: "我们采用企业级安全措施，包括数据加密、访问控制、定期安全审计等，确保您的数据安全。"
  }
]

const features = [
  {
    icon: Clock,
    title: "24/7 支持",
    desc: "全天候技术支持服务"
  },
  {
    icon: Globe,
    title: "全球服务",
    desc: "覆盖全球的服务网络"
  },
  {
    icon: Shield,
    title: "安全保障",
    desc: "企业级安全防护"
  },
  {
    icon: Zap,
    title: "快速响应",
    desc: "2小时内快速响应"
  }
]

export default function ContactUs() {
  return (
    <div className="relative isolate min-h-screen">
      {/* 增强的背景效果 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="grid-bg absolute inset-0 opacity-30" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-primary/20 bg-primary/5">
                <MessageCircle className="w-4 h-4 mr-2" />
                专业客服团队
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="text-gradient-modern">联系我们</span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              无论您有任何问题或建议，我们的专业团队都随时准备为您提供最优质的服务和支持
            </motion.p>

            <motion.div
              className="flex flex-wrap justify-center gap-4 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              {features.map((feature, index) => (
                <div key={feature.title} className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
                  <feature.icon className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium">{feature.title}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* 联系方式卡片 */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">多种联系方式</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              选择最适合您的联系方式，我们将为您提供专业、及时的服务
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${method.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                  <CardContent className="p-8 relative">
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`p-4 rounded-2xl bg-gradient-to-br ${method.color} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <method.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
                            {method.title}
                          </h3>
                          {method.available && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              在线
                            </Badge>
                          )}
                        </div>
                        <p className="text-lg font-medium mb-2 text-foreground">{method.value}</p>
                        <p className="text-sm text-muted-foreground mb-4">{method.desc}</p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                    >
                      立即联系
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 联系表单 */}
      <ContactUsForm />

      {/* 部门联系方式 */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">专业团队</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              我们的专业团队随时为您提供针对性的服务和支持
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {departments.map((dept, index) => (
              <motion.div
                key={dept.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${dept.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                  <CardContent className="p-6 relative">
                    <div className="text-center">
                      <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${dept.color} shadow-lg group-hover:scale-110 transition-transform duration-300 mb-4`}>
                        <dept.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                        {dept.title}
                      </h3>
                      <p className="text-sm font-medium text-primary mb-2">{dept.email}</p>
                      <p className="text-sm text-muted-foreground mb-4">{dept.desc}</p>
                      <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>响应时间: {dept.responseTime}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="mx-auto max-w-4xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">常见问题</h2>
            <p className="text-muted-foreground">
              快速找到您需要的答案
            </p>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-2 rounded-lg bg-primary/10 mt-1">
                        <HelpCircle className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">还有其他问题？</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              我们的专业团队随时准备为您提供帮助，无论是技术支持还是商务咨询
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                <MessageCircle className="w-5 h-5 mr-2" />
                在线客服
              </Button>
              <Button variant="outline" size="lg">
                <Phone className="w-5 h-5 mr-2" />
                电话咨询
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}