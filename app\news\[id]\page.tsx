import { NewsDetail } from "./components/NewsDetail"

// 获取所有新闻ID用于静态生成
async function getNewsIds() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/news`)
  const news = await res.json()
  return news.map((item: { id: string }) => item.id)
}

// 获取单个新闻详情
async function getNewsById(id: string) {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/news/${id}`)
  if (!res.ok) return null
  return res.json()
}

export async function generateStaticParams() {
  const ids = await getNewsIds()
  return ids.map((id: string) => ({
    id: id,
  }))
}

export default async function NewsPost({ params }: { params: { id: string } }) {
  const news = await getNewsById(params.id)
  if (!news) return null
  return <NewsDetail news={news} />
} 