"use client"

import { ProductCard } from "./components/ProductCard"
import { ProductHero } from "./components/ProductHero"
import { ProductCategories } from "./components/ProductCategories"
import { ProductFeatures } from "./components/ProductFeatures"
import { ProductStats } from "./components/ProductStats"
import { ProductCTA } from "./components/ProductCTA"
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { products } from "@/lib/products-data"

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

export default function Products() {
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState<string>('')

  useEffect(() => {
    let filtered = products

    // 按分类筛选
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.category === selectedCategory)
    }

    // 按搜索关键词筛选
    if (searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredProducts(filtered)
  }, [selectedCategory, searchQuery])

  const categories = [
    { id: 'all', name: '全部产品', count: products.length },
    { id: 'AI服务', name: 'AI服务', count: products.filter(p => p.category === 'AI服务').length },
    { id: '云计算', name: '云计算', count: products.filter(p => p.category === '云计算').length },
    { id: '教育科技', name: '教育科技', count: products.filter(p => p.category === '教育科技').length },
  ]

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <ProductHero
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      {/* Product Categories */}
      <ProductCategories
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={setSelectedCategory}
      />

      {/* Product Grid */}
      <section className="py-16 sm:py-24">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          {filteredProducts.length === 0 ? (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2">未找到相关产品</h3>
              <p className="text-slate-600">请尝试其他搜索关键词或选择不同的分类</p>
            </motion.div>
          ) : (
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {filteredProducts.map((product, index) => (
                <motion.div
                  key={product.slug}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <ProductCard product={product} index={index} />
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Product Features */}
      <ProductFeatures />

      {/* Product Stats */}
      <ProductStats />

      {/* CTA Section */}
      <ProductCTA />
    </div>
  )
}