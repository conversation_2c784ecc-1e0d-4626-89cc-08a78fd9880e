"use client"
import { But<PERSON> } from "@/components/ui/button"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="py-24 sm:py-32 text-center">
      <h2 className="text-2xl font-bold mb-4">出错了</h2>
      <p className="text-muted-foreground mb-8">{error.message}</p>
      <Button onClick={reset}>重试</Button>
    </div>
  )
} 