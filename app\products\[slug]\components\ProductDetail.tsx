"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Check, ArrowRight, Star, Download, Share2, Bookmark } from "lucide-react"
import Link from "next/link"
import {
  Layers,
  Shield,
  Zap,
  Settings,
  Users,
  BarChart,
  Cloud,
  Lock,
  Rocket,
  Code,
  LineChart,
  Database,
  Network,
  Server,
  Cpu,
  Target,
  Eye,
  MapPin,
  Tag,
  Heart,
  TrendingUp,
  Monitor,
  DollarSign,
  BookOpen,
  FileText,
  FileCheck,
  Award,
  Calendar,
  Scissors,
  CheckCircle,
  Globe,
  type LucideIcon
} from "lucide-react"
import Image from "next/image"
import { useState } from "react"
import { Play } from "lucide-react"
import { motion } from "framer-motion"

interface Benefit {
  title: string;
  description: string;
}

interface Feature {
  name: string;
  description: string;
  icon?: string;
}

interface FeatureCategory {
  title: string;
  description?: string;
  features: Feature[];
}

interface TechSpecs {
  deployment: string;
  security: string;
  availability: string;
  support: string;
}

interface Product {
  name: string;
  description: string;
  features: string[];
  techSpecs?: TechSpecs;
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
  featureList?: FeatureCategory[];
}

interface ProductDetailProps {
  product: Product;
}

const ICON_MAP: Record<string, LucideIcon> = {
  Layers: Layers,
  Shield: Shield,
  Zap: Zap,
  Settings: Settings,
  Users: Users,
  BarChart: BarChart,
  Cloud: Cloud,
  Lock: Lock,
  Rocket: Rocket,
  Code: Code,
  LineChart: LineChart,
  Database: Database,
  Network: Network,
  Server: Server,
  Cpu: Cpu,
  Target: Target,
  Eye: Eye,
  MapPin: MapPin,
  Tag: Tag,
  Heart: Heart,
  TrendingUp: TrendingUp,
  Monitor: Monitor,
  DollarSign: DollarSign,
  BookOpen: BookOpen,
  FileText: FileText,
  FileCheck: FileCheck,
  Award: Award,
  Calendar: Calendar,
  Scissors: Scissors,
  CheckCircle: CheckCircle,
  Globe: Globe
}

export function ProductDetail({ product }: ProductDetailProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)

  const renderIcon = (iconName?: string) => {
    if (!iconName) return null

    const IconComponent = ICON_MAP[iconName]
    if (!IconComponent) {
      console.warn(`Icon ${iconName} not found in ICON_MAP`)
      return <div className="w-6 h-6 bg-blue-500/20 rounded" />
    }

    return (
      <IconComponent
        className="w-6 h-6 text-blue-600 group-hover:scale-110 transition-transform"
      />
    )
  }

  return (
    <div className="relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50/30 via-white to-slate-50" />
      <div className="absolute top-0 right-0 w-96 h-96 rounded-full blur-3xl opacity-20 animate-float" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(99 102 241))' }} />

      <div className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          {/* Hero Section - 产品标题和描述 */}
          <motion.div
            className="mx-auto max-w-4xl text-center mb-20"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* 面包屑导航 */}
            <div className="flex items-center justify-center gap-2 mb-8 text-sm text-slate-600">
              <Link href="/products" className="hover:text-blue-600 transition-colors">产品</Link>
              <ArrowRight className="h-4 w-4" />
              <span className="text-blue-600 font-medium">{product.name}</span>
            </div>

            {/* 产品标题 */}
            <h1 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
              {product.name}
            </h1>

            {/* 产品描述 */}
            <p className="text-xl leading-relaxed text-slate-600 max-w-3xl mx-auto mb-10">
              {product.description}
            </p>

            {/* 操作按钮组 */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
              <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
                <Link href="/contact-us">
                  立即咨询
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>

              <Button
                variant="outline"
                className="px-8 py-4 text-lg border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all duration-300"
              >
                <Download className="mr-2 h-5 w-5" />
                下载资料
              </Button>
            </div>

            {/* 快速操作 */}
            <div className="flex items-center justify-center gap-6 text-sm text-slate-500">
              <button
                className="flex items-center gap-2 hover:text-blue-600 transition-colors"
                onClick={() => setIsBookmarked(!isBookmarked)}
              >
                <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-blue-600 text-blue-600' : ''}`} />
                {isBookmarked ? '已收藏' : '收藏'}
              </button>
              <button className="flex items-center gap-2 hover:text-blue-600 transition-colors">
                <Share2 className="h-4 w-4" />
                分享
              </button>
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="ml-1">4.9 (128 评价)</span>
              </div>
            </div>
          </motion.div>

          {/* 主要特性部分 */}
          <motion.div
            className="mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold tracking-tight text-slate-800 mb-4">核心功能特性</h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                强大的功能特性，满足您的各种业务需求
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {product.features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="card-modern p-6 h-full hover-glow transition-all duration-300 hover:scale-105">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                          <CheckCircle className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-slate-800 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                          {feature}
                        </h3>
                        <p className="text-sm text-slate-600 leading-relaxed">
                          专业的{feature}解决方案，提供高效可靠的服务体验
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* 技术规格部分 */}
          {product.techSpecs && (
            <motion.div
              className="mb-20"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-slate-800 mb-4">技术规格</h2>
                <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                  企业级技术标准，保障系统稳定可靠运行
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {Object.entries(product.techSpecs).map(([key, value], index) => (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <div className="card-modern p-6 text-center hover-glow transition-all duration-300 hover:scale-105">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center group-hover:from-blue-100 group-hover:to-indigo-100 transition-all duration-300">
                        {key === 'deployment' && <Cloud className="h-8 w-8 text-blue-600" />}
                        {key === 'security' && <Shield className="h-8 w-8 text-blue-600" />}
                        {key === 'availability' && <CheckCircle className="h-8 w-8 text-blue-600" />}
                        {key === 'support' && <Users className="h-8 w-8 text-blue-600" />}
                      </div>
                      <h3 className="font-semibold text-slate-800 mb-2 capitalize group-hover:text-blue-600 transition-colors duration-300">
                        {key === 'deployment' && '部署方式'}
                        {key === 'security' && '安全保障'}
                        {key === 'availability' && '可用性'}
                        {key === 'support' && '技术支持'}
                      </h3>
                      <p className="text-sm text-slate-600 leading-relaxed">
                        {value}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* 产品演示视频部分 */}
          {product.demoVideo && (
            <motion.div
              className="mb-20"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-slate-800 mb-4">产品演示</h2>
                <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                  通过视频演示深入了解产品功能和使用场景
                </p>
              </div>

              <div className="max-w-5xl mx-auto">
                <div className="relative">
                  <div className="aspect-video rounded-3xl overflow-hidden shadow-2xl bg-slate-900 relative group">
                    {!isPlaying && product.demoVideo.thumbnail ? (
                      <div
                        className="relative w-full h-full cursor-pointer"
                        onClick={() => setIsPlaying(true)}
                      >
                        <Image
                          src={product.demoVideo.thumbnail}
                          alt="视频预览"
                          fill
                          className="object-cover transition-transform group-hover:scale-105 duration-500"
                          priority
                        />
                        {/* 播放按钮覆盖层 */}
                        <div className="absolute inset-0 flex items-center justify-center bg-black/40 group-hover:bg-black/50 transition-all duration-300">
                          <motion.div
                            className="w-24 h-24 rounded-full bg-white/95 flex items-center justify-center shadow-2xl"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Play className="w-12 h-12 text-blue-600 fill-blue-600 translate-x-1" />
                          </motion.div>
                        </div>
                        {/* 视频信息覆盖层 */}
                        <div className="absolute bottom-0 inset-x-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-8">
                          <div className="flex items-center justify-between text-white">
                            <div>
                              <h3 className="text-xl font-semibold mb-2">产品功能演示</h3>
                              <p className="text-white/80">了解完整的产品功能和操作流程</p>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-white/60">视频时长</div>
                              <div className="text-lg font-medium">5:32</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="w-full h-full">
                        <iframe
                          src={`${product.demoVideo.url}${isPlaying ? '?autoplay=1' : ''}`}
                          className="w-full h-full rounded-3xl"
                          allow="autoplay; fullscreen"
                          allowFullScreen
                          title="产品演示视频"
                        />
                      </div>
                    )}
                  </div>

                  {/* 装饰性背景 */}
                  <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-2xl -z-10 opacity-50" />
                </div>

                {/* 视频下方的补充信息 */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                  <div className="p-4">
                    <div className="text-2xl font-bold text-blue-600 mb-2">5分钟</div>
                    <div className="text-sm text-slate-600">快速了解产品</div>
                  </div>
                  <div className="p-4">
                    <div className="text-2xl font-bold text-blue-600 mb-2">高清画质</div>
                    <div className="text-sm text-slate-600">清晰展示细节</div>
                  </div>
                  <div className="p-4">
                    <div className="text-2xl font-bold text-blue-600 mb-2">实际案例</div>
                    <div className="text-sm text-slate-600">真实使用场景</div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* 详细功能列表部分 */}
          {product.featureList && product.featureList.length > 0 && (
            <motion.div
              className="mb-20"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight text-slate-800 mb-4">功能详情</h2>
                <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                  深入了解每个功能模块的具体能力和应用场景
                </p>
              </div>

              <div className="space-y-24">
                {product.featureList.map((category, categoryIndex) => (
                  <motion.div
                    key={categoryIndex}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
                    viewport={{ once: true }}
                  >
                    {/* 分类标题 */}
                    <div className="text-center mb-12">
                      <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-blue-50 border border-blue-200 mb-6">
                        <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                          <span className="text-white font-bold text-sm">{categoryIndex + 1}</span>
                        </div>
                        <h3 className="text-xl font-bold text-blue-600">{category.title}</h3>
                      </div>
                      {category.description && (
                        <p className="text-slate-600 max-w-2xl mx-auto">{category.description}</p>
                      )}
                    </div>

                    {/* 功能卡片网格 */}
                    <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                      {category.features.map((feature, featureIndex) => (
                        <motion.div
                          key={featureIndex}
                          initial={{ opacity: 0, y: 20 }}
                          whileInView={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: featureIndex * 0.1 }}
                          viewport={{ once: true }}
                          className="group"
                        >
                          <div className="card-modern p-8 h-full hover-glow transition-all duration-300 hover:scale-105">
                            {/* 图标 */}
                            {feature.icon && (
                              <div className="relative mb-6">
                                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                  {renderIcon(feature.icon)}
                                </div>
                                <div className="absolute inset-0 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />
                              </div>
                            )}

                            {/* 内容 */}
                            <h4 className="text-lg font-bold text-slate-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                              {feature.name}
                            </h4>
                            <p className="text-slate-600 leading-relaxed text-sm">
                              {feature.description}
                            </p>

                            {/* 装饰性底边 */}
                            <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-blue-500 to-blue-600" />
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* 产品优势部分 */}
          {product.benefits && product.benefits.length > 0 && (
            <motion.div
              className="mb-20"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold tracking-tight text-slate-800 mb-4">产品优势</h2>
                <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                  选择我们的理由，让您的业务更上一层楼
                </p>
              </div>

              <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                {product.benefits.map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <div className="card-modern p-8 h-full hover-glow transition-all duration-300 hover:scale-105 text-center">
                      {/* 数字标识 */}
                      <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <span className="text-white font-bold text-xl">{index + 1}</span>
                      </div>

                      <h3 className="text-xl font-bold text-slate-800 mb-4 group-hover:text-green-600 transition-colors duration-300">
                        {benefit.title}
                      </h3>
                      <p className="text-slate-600 leading-relaxed">
                        {benefit.description}
                      </p>

                      {/* 装饰性底边 */}
                      <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-green-500 to-green-600" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}

          {/* 最终CTA部分 */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="card-modern p-12 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
              <h2 className="text-3xl font-bold text-slate-800 mb-4">准备开始了吗？</h2>
              <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
                立即联系我们的专业团队，获取个性化解决方案和专业咨询服务
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
                  <Link href="/contact-us">
                    立即咨询
                    <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>

                <Button
                  variant="outline"
                  className="px-8 py-4 text-lg border-blue-200 hover:bg-blue-50 hover:border-blue-300 transition-all duration-300"
                >
                  预约演示
                </Button>
              </div>

              {/* 联系信息 */}
              <div className="mt-8 pt-8 border-t border-blue-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-slate-600">
                  <div className="flex items-center justify-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span>专业团队支持</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Shield className="h-4 w-4 text-blue-600" />
                    <span>数据安全保障</span>
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600" />
                    <span>7x24小时服务</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}